2025-08-20T15:33:12.838Z - === SERVICE SCRIPT STARTING ===
2025-08-20T15:33:12.848Z - Process ID: 19440
2025-08-20T15:33:12.850Z - Node version: v22.14.0
2025-08-20T15:33:12.850Z - Current directory: E:\projects\Windows-Agent-App\Windows-Client-Application
2025-08-20T15:33:12.851Z - Working directory: E:\projects\Windows-Agent-App\Windows-Client-Application
2025-08-20T15:33:12.852Z - Command line arguments: C:\Program Files\nodejs\node.exe E:\projects\Windows-Agent-App\Windows-Client-Application\service.js install
2025-08-20T15:33:12.853Z - Checking required files...
2025-08-20T15:33:12.854Z - Agent script path: E:\projects\Windows-Agent-App\Windows-Client-Application\agent.js
2025-08-20T15:33:12.855Z - Config file path: E:\projects\Windows-Agent-App\Windows-Client-Application\agent-config.json
2025-08-20T15:33:12.857Z - Agent script found
2025-08-20T15:33:12.858Z - Config file found
2025-08-20T15:33:12.860Z - Service configuration created:
2025-08-20T15:33:12.860Z - Service name: WindowsAgentService
2025-08-20T15:33:12.861Z - Service description: Background Windows Asset Agent
2025-08-20T15:33:12.862Z - Script path: E:\projects\Windows-Agent-App\Windows-Client-Application\agent.js
2025-08-20T15:33:12.863Z - Node options: --harmony --max_old_space_size=4096
2025-08-20T15:33:12.863Z - Processing command: install
2025-08-20T15:33:12.864Z - === INSTALLING SERVICE ===
2025-08-20T15:33:12.865Z - Calling service.install()...
2025-08-20T15:33:12.871Z - === SERVICE SCRIPT COMPLETED ===
2025-08-20T15:33:20.673Z - === SERVICE INSTALL EVENT TRIGGERED ===
2025-08-20T15:33:20.674Z - Service installed successfully, attempting to start...
2025-08-20T15:33:20.682Z - Service start command issued
2025-08-20T15:33:25.032Z - === SERVICE START EVENT TRIGGERED ===
2025-08-20T15:33:25.033Z - Service started successfully
2025-08-20T15:33:25.034Z - Service process ID: undefined
2025-08-20T15:33:25.035Z - === SERVICE STARTED ===
2025-08-20T15:33:25.036Z - Service is now running
2025-08-20T15:34:28.485Z - === SERVICE SCRIPT STARTING ===
2025-08-20T15:34:28.494Z - Process ID: 14040
2025-08-20T15:34:28.496Z - Node version: v22.14.0
2025-08-20T15:34:28.497Z - Current directory: E:\projects\Windows-Agent-App\Windows-Client-Application
2025-08-20T15:34:28.498Z - Working directory: E:\projects\Windows-Agent-App\Windows-Client-Application
2025-08-20T15:34:28.499Z - Command line arguments: C:\Program Files\nodejs\node.exe E:\projects\Windows-Agent-App\Windows-Client-Application\service.js uninstall
2025-08-20T15:34:28.499Z - Checking required files...
2025-08-20T15:34:28.500Z - Agent script path: E:\projects\Windows-Agent-App\Windows-Client-Application\agent.js
2025-08-20T15:34:28.501Z - Config file path: E:\projects\Windows-Agent-App\Windows-Client-Application\agent-config.json
2025-08-20T15:34:28.503Z - Agent script found
2025-08-20T15:34:28.504Z - Config file found
2025-08-20T15:34:28.505Z - Service configuration created:
2025-08-20T15:34:28.506Z - Service name: WindowsAgentService
2025-08-20T15:34:28.506Z - Service description: Background Windows Asset Agent
2025-08-20T15:34:28.507Z - Script path: E:\projects\Windows-Agent-App\Windows-Client-Application\agent.js
2025-08-20T15:34:28.508Z - Node options: --harmony --max_old_space_size=4096
2025-08-20T15:34:28.508Z - Processing command: uninstall
2025-08-20T15:34:28.509Z - === UNINSTALLING SERVICE ===
2025-08-20T15:34:28.510Z - Calling service.uninstall()...
2025-08-20T15:34:28.538Z - === SERVICE SCRIPT COMPLETED ===
2025-08-20T15:34:32.503Z - === SERVICE STOP EVENT TRIGGERED ===
2025-08-20T15:34:32.504Z - Service stopped
2025-08-20T15:34:32.505Z - === SERVICE STOPPED ===
2025-08-20T15:34:32.506Z - Service has stopped
2025-08-20T15:34:39.333Z - === SERVICE UNINSTALL EVENT TRIGGERED ===
2025-08-20T15:34:39.334Z - Service uninstalled successfully
2025-08-20T15:37:21.831Z - === SERVICE SCRIPT STARTING ===
2025-08-20T15:37:21.841Z - Process ID: 9568
2025-08-20T15:37:21.842Z - Node version: v22.14.0
2025-08-20T15:37:21.843Z - Current directory: E:\projects\Windows-Agent-App\Windows-Client-Application
2025-08-20T15:37:21.844Z - Working directory: E:\projects\Windows-Agent-App\Windows-Client-Application
2025-08-20T15:37:21.845Z - Command line arguments: C:\Program Files\nodejs\node.exe E:\projects\Windows-Agent-App\Windows-Client-Application\service.js uninstall
2025-08-20T15:37:21.845Z - Checking required files...
2025-08-20T15:37:21.846Z - Agent script path: E:\projects\Windows-Agent-App\Windows-Client-Application\agent.js
2025-08-20T15:37:21.847Z - Config file path: E:\projects\Windows-Agent-App\Windows-Client-Application\agent-config.json
2025-08-20T15:37:21.849Z - Agent script found
2025-08-20T15:37:21.850Z - Config file found
2025-08-20T15:37:21.852Z - Service configuration created:
2025-08-20T15:37:21.853Z - Service name: WindowsAgentService
2025-08-20T15:37:21.853Z - Service description: Background Windows Asset Agent
2025-08-20T15:37:21.854Z - Script path: E:\projects\Windows-Agent-App\Windows-Client-Application\agent.js
2025-08-20T15:37:21.855Z - Node options: --harmony --max_old_space_size=4096
2025-08-20T15:37:21.856Z - Processing command: uninstall
2025-08-20T15:37:21.857Z - === UNINSTALLING SERVICE ===
2025-08-20T15:37:21.857Z - Calling service.uninstall()...
2025-08-20T15:37:21.859Z - === SERVICE SCRIPT COMPLETED ===
2025-08-20T15:37:49.115Z - === SERVICE SCRIPT STARTING ===
2025-08-20T15:37:49.124Z - Process ID: 14804
2025-08-20T15:37:49.125Z - Node version: v22.14.0
2025-08-20T15:37:49.126Z - Current directory: E:\projects\Windows-Agent-App\Windows-Client-Application
2025-08-20T15:37:49.127Z - Working directory: E:\projects\Windows-Agent-App\Windows-Client-Application
2025-08-20T15:37:49.128Z - Command line arguments: C:\Program Files\nodejs\node.exe E:\projects\Windows-Agent-App\Windows-Client-Application\service.js uninstall
2025-08-20T15:37:49.128Z - Checking required files...
2025-08-20T15:37:49.129Z - Agent script path: E:\projects\Windows-Agent-App\Windows-Client-Application\agent.js
2025-08-20T15:37:49.130Z - Config file path: E:\projects\Windows-Agent-App\Windows-Client-Application\agent-config.json
2025-08-20T15:37:49.132Z - Agent script found
2025-08-20T15:37:49.133Z - Config file found
2025-08-20T15:37:49.135Z - Service configuration created:
2025-08-20T15:37:49.135Z - Service name: WindowsAgentService
2025-08-20T15:37:49.136Z - Service description: Background Windows Asset Agent
2025-08-20T15:37:49.137Z - Script path: E:\projects\Windows-Agent-App\Windows-Client-Application\agent.js
2025-08-20T15:37:49.137Z - Node options: --harmony --max_old_space_size=4096
2025-08-20T15:37:49.138Z - Processing command: uninstall
2025-08-20T15:37:49.139Z - === UNINSTALLING SERVICE ===
2025-08-20T15:37:49.139Z - Calling service.uninstall()...
2025-08-20T15:37:49.141Z - === SERVICE SCRIPT COMPLETED ===
