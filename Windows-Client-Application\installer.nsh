!macro customInstall
  MessageBox MB_OK "Custom install macro triggered at $INSTDIR"
  
  ; Log installation details
  DetailPrint "=== WINDOWS AGENT INSTALLATION STARTING ==="
  DetailPrint "Installation directory: $INSTDIR"
  
  ; Check if node.exe exists
  DetailPrint "Checking for node.exe..."
  IfFileExists "$INSTDIR\resources\node.exe" nodeExists 0
    DetailPrint "ERROR: node.exe not found at $INSTDIR\resources"
    MessageBox MB_ICONSTOP "node.exe not found at $INSTDIR\resources"
    Goto installEnd
  nodeExists:
    DetailPrint "node.exe found at $INSTDIR\resources"
  
  ; Check if service.js exists
  DetailPrint "Checking for service.js..."
  IfFileExists "$INSTDIR\resources\service.js" serviceExists 0
    DetailPrint "ERROR: service.js not found at $INSTDIR\resources"
    MessageBox MB_ICONSTOP "service.js not found at $INSTDIR\resources"
    Goto installEnd
  serviceExists:
    DetailPrint "service.js found at $INSTDIR\resources"
  
  ; Check if agent.js exists
  DetailPrint "Checking for agent.js..."
  IfFileExists "$INSTDIR\resources\agent.js" agentExists 0
    DetailPrint "ERROR: agent.js not found at $INSTDIR\resources"
    MessageBox MB_ICONSTOP "agent.js not found at $INSTDIR\resources"
    Goto installEnd
  agentExists:
    DetailPrint "agent.js found at $INSTDIR\resources"
  
  ; Check if agent-config.json exists
  DetailPrint "Checking for agent-config.json..."
  IfFileExists "$INSTDIR\resources\agent-config.json" configExists 0
    DetailPrint "WARNING: agent-config.json not found, will use defaults"
  configExists:
    DetailPrint "agent-config.json check completed"
  
  ; All files found, proceed with service installation
  DetailPrint "=== INSTALLING WINDOWS SERVICE ==="
  DetailPrint "Executing: $INSTDIR\resources\node.exe $INSTDIR\resources\service.js install"
  
  ; Execute the service installation
  ExecWait '"$INSTDIR\resources\node.exe" "$INSTDIR\resources\service.js" install' $0
  
  DetailPrint "Service installation completed with exit code: $0"
  
  ; Check if service installation was successful
  IntCmp $0 0 installSuccess installFailed installFailed
  installSuccess:
    DetailPrint "Service installation successful"
    MessageBox MB_OK "Windows Agent service installed successfully!"
    Goto installEnd
  installFailed:
    DetailPrint "Service installation failed with exit code: $0"
    MessageBox MB_ICONEXCLAMATION "Service installation may have failed. Check service.log for details."
  
  installEnd:
    DetailPrint "=== WINDOWS AGENT INSTALLATION COMPLETED ==="
!macroend

!macro customUnInstall
  DetailPrint "=== WINDOWS AGENT UNINSTALLATION STARTING ==="
  DetailPrint "Installation directory: $INSTDIR"
  
  ; Stop and uninstall the service
  DetailPrint "Stopping and uninstalling Windows Agent service..."
  ExecWait '"$INSTDIR\resources\node.exe" "$INSTDIR\resources\service.js" uninstall' $0
  
  DetailPrint "Service uninstallation completed with exit code: $0"
  
  ; Clean up log files
  DetailPrint "Cleaning up log files..."
  Delete "$INSTDIR\resources\agent.log"
  Delete "$INSTDIR\resources\service.log"
  Delete "$INSTDIR\resources\agent-error.log"
  Delete "C:\agent.log"
  Delete "C:\service.log"
  
  DetailPrint "=== WINDOWS AGENT UNINSTALLATION COMPLETED ==="
  MessageBox MB_OK "Custom uninstall macro triggered at $INSTDIR"
!macroend

Section "Install"
  DetailPrint "=== MAIN INSTALL SECTION RUNNING ==="
  MessageBox MB_OK "Main Install Section Running"
  !insertmacro customInstall
SectionEnd
