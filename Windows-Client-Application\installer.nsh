!macro customInstall
  MessageBox MB_OK "Custom install macro triggered at $INSTDIR"

  ; Log installation details
  DetailPrint "=== WINDOWS AGENT INSTALLATION STARTING ==="
  DetailPrint "Installation directory: $INSTDIR"

  ; Note: This installer should be run as Administrator for service installation
  DetailPrint "Note: Administrator privileges are required for service installation"
  
  ; Check if node.exe exists
  DetailPrint "Checking for node.exe..."
  IfFileExists "$INSTDIR\resources\node.exe" nodeExists 0
    DetailPrint "ERROR: node.exe not found at $INSTDIR\resources"
    MessageBox MB_ICONSTOP "node.exe not found at $INSTDIR\resources"
    Goto installEnd
  nodeExists:
    DetailPrint "node.exe found at $INSTDIR\resources"
  
  ; Check if service.js exists
  DetailPrint "Checking for service.js..."
  IfFileExists "$INSTDIR\resources\service.js" serviceExists 0
    DetailPrint "ERROR: service.js not found at $INSTDIR\resources"
    MessageBox MB_ICONSTOP "service.js not found at $INSTDIR\resources"
    Goto installEnd
  serviceExists:
    DetailPrint "service.js found at $INSTDIR\resources"
  
  ; Check if agent.js exists
  DetailPrint "Checking for agent.js..."
  IfFileExists "$INSTDIR\resources\agent.js" agentExists 0
    DetailPrint "ERROR: agent.js not found at $INSTDIR\resources"
    MessageBox MB_ICONSTOP "agent.js not found at $INSTDIR\resources"
    Goto installEnd
  agentExists:
    DetailPrint "agent.js found at $INSTDIR\resources"
  
  ; Check if agent-config.json exists
  DetailPrint "Checking for agent-config.json..."
  IfFileExists "$INSTDIR\resources\agent-config.json" configExists 0
    DetailPrint "WARNING: agent-config.json not found, will use defaults"
  configExists:
    DetailPrint "agent-config.json check completed"
  
  ; All files found, proceed with service installation
  DetailPrint "=== INSTALLING WINDOWS SERVICE ==="
  DetailPrint "Executing: $INSTDIR\resources\node.exe $INSTDIR\resources\service.js install"
  
  ; Change to the resources directory before executing
  SetOutPath "$INSTDIR\resources"
  DetailPrint "Changed working directory to: $INSTDIR\resources"

  ; Execute the service installation with enhanced error handling and timeout
  DetailPrint "Executing service installation command..."
  DetailPrint "Command: $\"$INSTDIR\resources\node.exe$\" $\"$INSTDIR\resources\service.js$\" install"

  ; Execute the service installation
  ExecWait '"$INSTDIR\resources\node.exe" "$INSTDIR\resources\service.js" install' $0

  DetailPrint "Service installation completed with exit code: $0"

  ; Enhanced error checking and logging
  IntCmp $0 0 installSuccess checkLogs checkLogs

  checkLogs:
    DetailPrint "Non-zero exit code detected, checking for service.log..."
    IfFileExists "$INSTDIR\resources\service.log" showLogLocation noLogFound

  showLogLocation:
    DetailPrint "Service log found at: $INSTDIR\resources\service.log"
    MessageBox MB_ICONEXCLAMATION "Service installation returned exit code $0.$\r$\nPlease check the service log at:$\r$\n$INSTDIR\resources\service.log$\r$\nfor detailed error information."
    Goto installEnd

  noLogFound:
    DetailPrint "No service.log found, installation may have failed early"
    MessageBox MB_ICONEXCLAMATION "Service installation failed with exit code $0.$\r$\nNo service log was created, which suggests the installation failed early.$\r$\nPlease run the installer as Administrator and ensure Node.js is properly installed."
    Goto installEnd

  installSuccess:
    DetailPrint "Service installation successful"
    MessageBox MB_OK "Windows Agent service installed successfully!"
    Goto installEnd
  
  installEnd:
    DetailPrint "=== WINDOWS AGENT INSTALLATION COMPLETED ==="
!macroend

!macro customUnInstall
  DetailPrint "=== WINDOWS AGENT UNINSTALLATION STARTING ==="
  DetailPrint "Installation directory: $INSTDIR"

  ; Change to resources directory for uninstall
  SetOutPath "$INSTDIR\resources"

  ; Stop and uninstall the service with timeout
  DetailPrint "Stopping and uninstalling Windows Agent service..."
  DetailPrint "Command: $\"$INSTDIR\resources\node.exe$\" $\"$INSTDIR\resources\service.js$\" uninstall"
  ExecWait '"$INSTDIR\resources\node.exe" "$INSTDIR\resources\service.js" uninstall' $0

  DetailPrint "Service uninstallation completed with exit code: $0"

  ; Check uninstall result
  IntCmp $0 0 uninstallSuccess uninstallWarning uninstallWarning
  uninstallSuccess:
    DetailPrint "Service uninstalled successfully"
    Goto cleanupFiles
  uninstallWarning:
    DetailPrint "Service uninstall returned exit code $0 - continuing with cleanup"

  cleanupFiles:
  
  ; Clean up log files
  DetailPrint "Cleaning up log files..."
  Delete "$INSTDIR\resources\agent.log"
  Delete "$INSTDIR\resources\@iing"
  Delete "$INSTDIR\resources\agent-error.log"
  Delete "C:\agent.log"
  Delete "C:\service.log"
  
  DetailPrint "=== WINDOWS AGENT UNINSTALLATION COMPLETED ==="
  MessageBox MB_OK "Custom uninstall macro triggered at $INSTDIR"
!macroend

Section "Install"
  DetailPrint "=== MAIN INSTALL SECTION RUNNING ==="
  MessageBox MB_OK "Main Install Section Running"
  !insertmacro customInstall
SectionEnd
