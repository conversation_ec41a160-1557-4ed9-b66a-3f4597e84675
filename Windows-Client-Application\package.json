{"name": "windows-client-application", "version": "1.0.0", "main": "main.js", "build": {"appId": "com.yourcompany.windowsagent", "productName": "Windows Agent", "asar": false, "files": ["main.js", "agent.js", "service.js", "agent-config.json", "node.exe", "installer.nsh", "package.json", "node_modules/**/*"], "extraResources": [{"from": "node.exe", "to": "node.exe"}, {"from": "agent-config.json", "to": "agent-config.json"}, {"from": "agent.js", "to": "agent.js"}, {"from": "service.js", "to": "service.js"}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}]}, "nsis": {"oneClick": false, "perMachine": true, "allowToChangeInstallationDirectory": true, "runAfterFinish": true, "include": "installer.nsh"}}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "electron-builder --win --x64"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"electron": "^37.2.3", "electron-builder": "^26.0.12"}, "dependencies": {"axios": "^1.10.0", "node-windows": "^1.0.0-beta.8", "systeminformation": "^5.27.7"}}