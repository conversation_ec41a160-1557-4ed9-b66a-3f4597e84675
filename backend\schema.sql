-- Users table
CREATE TABLE IF NOT EXISTS users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHA<PERSON>(64) NOT NULL UNIQUE,
  password_hash VARCHAR(255) NOT NULL,
  role_id INT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Roles table
CREATE TABLE IF NOT EXISTS roles (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHA<PERSON>(32) NOT NULL UNIQUE
);

-- API tokens for agents
CREATE TABLE IF NOT EXISTS api_tokens (
  id INT AUTO_INCREMENT PRIMARY KEY,
  token VARCHAR(128) NOT NULL UNIQUE,
  description VARCHAR(255),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  last_used TIMESTAMP NULL
);

-- Assets table (one per device)
CREATE TABLE IF NOT EXISTS assets (
  id INT AUTO_INCREMENT PRIMARY KEY,
  hostname <PERSON><PERSON><PERSON><PERSON>(128) NOT NULL,
  ip_addresses TEXT,
  mac_addresses TEXT,
  os_name <PERSON><PERSON><PERSON><PERSON>(128),
  os_version VARCHAR(64),
  os_arch VARCHAR(32),
  os_build VARCHAR(32),
  bios_serial VARCHAR(128),
  manufacturer VARCHAR(128),
  model VARCHAR(128),
  cpu_model VARCHAR(128),
  cpu_speed VARCHAR(32),
  cpu_cores INT,
  cpu_threads INT,
  ram_total BIGINT,
  ram_slots TEXT,
  disks JSON,
  gpus JSON,
  network_adapters JSON,
  location VARCHAR(128),
  last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Software inventory (per asset)
CREATE TABLE IF NOT EXISTS software (
  id INT AUTO_INCREMENT PRIMARY KEY,
  asset_id INT NOT NULL,
  name VARCHAR(255),
  publisher VARCHAR(255),
  install_date VARCHAR(32),
  version VARCHAR(64),
  serial_key VARCHAR(128),
  FOREIGN KEY (asset_id) REFERENCES assets(id) ON DELETE CASCADE
);

-- Audit log
CREATE TABLE IF NOT EXISTS audit_logs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT,
  action VARCHAR(255),
  details TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Insert default roles
INSERT IGNORE INTO roles (id, name) VALUES (1, 'admin'), (2, 'user'); 