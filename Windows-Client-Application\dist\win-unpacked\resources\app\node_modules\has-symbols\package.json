{"name": "has-symbols", "version": "1.1.0", "description": "Determine if the JS environment has Symbol support. Supports spec, or shams.", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/inspect-js/has-symbols.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "homepage": "https://github.com/ljharb/has-symbols#readme", "devDependencies": {"@arethetypeswrong/cli": "^0.17.0", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.0", "@types/core-js": "^2.5.8", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "core-js": "^2.6.12", "encoding": "^0.1.13", "eslint": "=8.8.0", "get-own-property-symbols": "^0.9.5", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows", "types"]}}