const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const { Parser } = require('json2csv');

function checkJwt(req, res, next) {
  const auth = req.headers['authorization'] || '';
  const token = auth.replace('Bearer ', '');
  if (!token) return res.status(401).json({ error: 'Missing token' });
  try {
    req.user = jwt.verify(token, process.env.JWT_SECRET);
    next();
  } catch {
    return res.status(401).json({ error: 'Invalid token' });
  }
}

// Middleware to check API token for agent POST
async function checkApiToken(req, res, next) {
  const db = req.app.get('db');
  const auth = req.headers['authorization'] || '';
  const token = auth.replace('Bearer ', '');
  if (!token) return res.status(401).json({ error: 'Missing API token' });
  const [rows] = await db.query('SELECT * FROM api_tokens WHERE token = ?', [token]);
  if (!rows.length) return res.status(401).json({ error: 'Invalid API token' });
  // Update last_used
  await db.query('UPDATE api_tokens SET last_used = NOW() WHERE id = ?', [rows[0].id]);
  next();
}

// POST /assets - receive asset data from agent
router.post('/', checkApiToken, async (req, res) => {
  const db = req.app.get('db');
  const { system, software } = req.body;
  if (!system || !system.hostname) return res.status(400).json({ error: 'Missing system info' });
  // Upsert asset
  const [result] = await db.query(
    `INSERT INTO assets (hostname, ip_addresses, mac_addresses, os_name, os_version, os_arch, os_build, bios_serial, manufacturer, model, cpu_model, cpu_speed, cpu_cores, cpu_threads, ram_total, ram_slots, disks, gpus, network_adapters, last_seen)
     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
     ON DUPLICATE KEY UPDATE
       ip_addresses=VALUES(ip_addresses), mac_addresses=VALUES(mac_addresses), os_name=VALUES(os_name), os_version=VALUES(os_version), os_arch=VALUES(os_arch), os_build=VALUES(os_build), bios_serial=VALUES(bios_serial), manufacturer=VALUES(manufacturer), model=VALUES(model), cpu_model=VALUES(cpu_model), cpu_speed=VALUES(cpu_speed), cpu_cores=VALUES(cpu_cores), cpu_threads=VALUES(cpu_threads), ram_total=VALUES(ram_total), ram_slots=VALUES(ram_slots), disks=VALUES(disks), gpus=VALUES(gpus), network_adapters=VALUES(network_adapters), last_seen=NOW()`,
    [
      system.hostname,
      JSON.stringify(system.ipAddresses),
      JSON.stringify(system.macAddresses),
      system.os?.name,
      system.os?.version,
      system.os?.arch,
      system.os?.build,
      system.biosSerial,
      system.manufacturer,
      system.model,
      system.cpu?.model,
      system.cpu?.speed,
      system.cpu?.cores,
      system.cpu?.threads,
      system.ram?.total,
      JSON.stringify(system.ram?.slots),
      JSON.stringify(system.disks),
      JSON.stringify(system.gpu),
      JSON.stringify(system.networkAdapters)
    ]
  );
  // Get asset id
  const [assetRows] = await db.query('SELECT id FROM assets WHERE hostname = ?', [system.hostname]);
  const assetId = assetRows[0].id;
  // Remove old software entries
  await db.query('DELETE FROM software WHERE asset_id = ?', [assetId]);
  // Insert new software entries
  if (Array.isArray(software)) {
    for (const app of software) {
      await db.query(
        'INSERT INTO software (asset_id, name, publisher, install_date, version) VALUES (?, ?, ?, ?, ?)',
        [assetId, app.name, app.publisher, app.installDate, app.version]
      );
    }
  }
  res.json({ message: 'Asset data stored' });
});

// GET /assets - list all assets (admin, JWT required)
router.get('/', checkJwt, async (req, res) => {
  const db = req.app.get('db');
  const [assets] = await db.query('SELECT * FROM assets');
  res.json(assets);
});

// GET /assets/:id - asset detail (with software)
router.get('/:id', checkJwt, async (req, res) => {
  const db = req.app.get('db');
  const [assets] = await db.query('SELECT * FROM assets WHERE id = ?', [req.params.id]);
  if (!assets.length) return res.status(404).json({ error: 'Asset not found' });
  const asset = assets[0];
  const [software] = await db.query('SELECT * FROM software WHERE asset_id = ?', [asset.id]);
  asset.software = software;
  res.json(asset);
});

// GET /assets/:id/software - software inventory for asset
router.get('/:id/software', checkJwt, async (req, res) => {
  const db = req.app.get('db');
  const [software] = await db.query('SELECT * FROM software WHERE asset_id = ?', [req.params.id]);
  res.json(software);
});

// GET /assets/summary - hardware/software summary
router.get('/summary/all', checkJwt, async (req, res) => {
  const db = req.app.get('db');
  // Total assets by location
  const [byLocation] = await db.query('SELECT location, COUNT(*) as count FROM assets GROUP BY location');
  // Assets per OS type
  const [byOs] = await db.query('SELECT os_name, COUNT(*) as count FROM assets GROUP BY os_name');
  // Outdated software (example: version < '2023')
  const [outdated] = await db.query("SELECT s.*, a.hostname FROM software s JOIN assets a ON s.asset_id = a.id WHERE s.version < '2023'");
  // Insufficient hardware (example: RAM < 4GB)
  const [lowRam] = await db.query('SELECT hostname, ram_total FROM assets WHERE ram_total < 4*1024*1024*1024');
  res.json({ byLocation, byOs, outdated, lowRam });
});

// GET /assets/search?hostname=&ip=&serial= - search assets
router.get('/search', checkJwt, async (req, res) => {
  const db = req.app.get('db');
  const { hostname, ip, serial } = req.query;
  let sql = 'SELECT * FROM assets WHERE 1=1';
  const params = [];
  if (hostname) {
    sql += ' AND hostname LIKE ?';
    params.push(`%${hostname}%`);
  }
  if (ip) {
    sql += ' AND ip_addresses LIKE ?';
    params.push(`%${ip}%`);
  }
  if (serial) {
    sql += ' AND bios_serial LIKE ?';
    params.push(`%${serial}%`);
  }
  const [assets] = await db.query(sql, params);
  res.json(assets);
});

// GET /assets/export/csv - export all assets as CSV
router.get('/export/csv', checkJwt, async (req, res) => {
  const db = req.app.get('db');
  const [assets] = await db.query('SELECT * FROM assets');
  const fields = Object.keys(assets[0] || {});
  const parser = new Parser({ fields });
  const csv = parser.parse(assets);
  res.header('Content-Type', 'text/csv');
  res.attachment('assets.csv');
  res.send(csv);
});

module.exports = router; 