const si = require('systeminformation');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Enhanced logging function with multiple fallback locations
function log(message, isError = false) {
  const timestamp = new Date().toISOString();
  const logMessage = `${timestamp} - ${isError ? 'ERROR: ' : ''}${message}\n`;
  
  // Try multiple log locations in order of preference
  const logPaths = [
    path.join(__dirname, 'agent.log'),
    path.join(process.cwd(), 'agent.log'),
    path.join(process.env.TEMP || 'C:\\temp', 'agent.log'),
    'C:\\agent.log',
    path.join(__dirname, 'agent-error.log') // Fallback to original location
  ];
  
  let logged = false;
  for (const logPath of logPaths) {
    try {
      fs.appendFileSync(logPath, logMessage);
      logged = true;
      break;
    } catch (e) {
      // Continue to next path if this one fails
    }
  }
  
  // Also output to console
  if (isError) {
    console.error(`[AGENT ERROR] ${message}`);
  } else {
    console.log(`[AGENT] ${message}`);
  }
  
  if (!logged) {
    console.error(`[AGENT] Failed to write to any log file: ${message}`);
  }
}

// Log startup information
log('=== AGENT STARTING ===');
log(`Process ID: ${process.pid}`);
log(`Node version: ${process.version}`);
log(`Current directory: ${__dirname}`);
log(`Working directory: ${process.cwd()}`);
log(`Environment: ${process.env.NODE_ENV || 'production'}`);

// Configurable interval (default: 24 hours)
const CONFIG_PATH = path.join(__dirname, 'agent-config.json');
let config = { intervalHours: 24, apiUrl: 'http://localhost:5000/assets', apiToken: 'f3bf932d76cf8d91baae19b6fab90c5bdc7db1261b92bd01cd33045a8fe03e05' };

log(`Looking for config at: ${CONFIG_PATH}`);
if (fs.existsSync(CONFIG_PATH)) {
  try { 
    const configData = JSON.parse(fs.readFileSync(CONFIG_PATH, 'utf-8'));
    config = { ...config, ...configData };
    log(`Config loaded successfully from file`);
    log(`API URL: ${config.apiUrl}`);
    log(`API Token: ${config.apiToken.substring(0, 10)}...`);
    log(`Interval: ${config.intervalHours} hours`);
  } catch (e) {
    log(`Error loading config file: ${e.message}`, true);
    log(`Using default config values`);
  }
} else {
  log(`Config file not found at ${CONFIG_PATH}, using defaults`);
  log(`Default API URL: ${config.apiUrl}`);
  log(`Default API Token: ${config.apiToken.substring(0, 10)}...`);
}

async function collectSystemInfo() {
  try {
    log('Starting system information collection...');
    const [system, os, bios, cpu, mem, disk, graphics, network] = await Promise.all([
      si.system(),
      si.osInfo(),
      si.bios(),
      si.cpu(),
      si.mem(),
      si.diskLayout(),
      si.graphics(),
      si.networkInterfaces()
    ]);
    
    const systemInfo = {
      hostname: os.hostname,
      ipAddresses: network.map(n => n.ip4),
      macAddresses: network.map(n => n.mac),
      os: { name: os.distro, version: os.release, arch: os.arch, build: os.build },
      biosSerial: bios.serial,
      manufacturer: system.manufacturer,
      model: system.model,
      cpu: { model: cpu.manufacturer + ' ' + cpu.brand, speed: cpu.speed, cores: cpu.cores, threads: cpu.processors },
      ram: { total: mem.total, slots: mem.layout },
      disks: disk.map(d => ({ model: d.name, size: d.size, type: d.type, health: d.smartStatus })),
      gpu: graphics.controllers.map(g => ({ model: g.model, driver: g.driverVersion })),
      networkAdapters: network.map(n => ({ name: n.iface, status: n.operstate }))
    };
    
    log(`System info collected successfully`);
    log(`Hostname: ${systemInfo.hostname}`);
    log(`OS: ${systemInfo.os.name} ${systemInfo.os.version}`);
    log(`Manufacturer: ${systemInfo.manufacturer}`);
    log(`Model: ${systemInfo.model}`);
    log(`CPU: ${systemInfo.cpu.model}`);
    log(`RAM: ${Math.round(systemInfo.ram.total / (1024 * 1024 * 1024))} GB`);
    log(`Disks: ${systemInfo.disks.length} found`);
    log(`Network interfaces: ${systemInfo.networkAdapters.length} found`);
    
    return systemInfo;
  } catch (e) {
    log(`Error collecting system info: ${e.message}`, true);
    log(`System info error stack: ${e.stack}`, true);
    throw e;
  }
}

async function collectSoftwareInfo() {
  try {
    log('Starting software information collection...');
    // For Windows, get installed software via WMIC
    const { exec } = require('child_process');
    return new Promise((resolve) => {
      exec('wmic product get Name,Version,Vendor,InstallDate', { maxBuffer: 1024 * 1024 * 10 }, (err, stdout) => {
        if (err) {
          log(`Error executing WMIC command: ${err.message}`, true);
          log(`WMIC error stack: ${err.stack}`, true);
          return resolve([]);
        }
        
        const lines = stdout.split('\n').filter(l => l.trim()).slice(1);
        const apps = lines.map(line => {
          const parts = line.trim().split(/\s{2,}/);
          return { name: parts[0], publisher: parts[1], installDate: parts[2], version: parts[3] };
        });
        
        log(`Software info collected: ${apps.length} applications found`);
        if (apps.length > 0) {
          log(`Sample applications: ${apps.slice(0, 3).map(a => a.name).join(', ')}`);
        }
        resolve(apps);
      });
    });
  } catch (e) {
    log(`Error in collectSoftwareInfo: ${e.message}`, true);
    log(`Software info error stack: ${e.stack}`, true);
    return [];
  }
}

async function sendData() {
  const startTime = Date.now();
  try {
    log('=== STARTING DATA SEND ===');
    log(`Target API URL: ${config.apiUrl}`);
    log(`Using API token: ${config.apiToken.substring(0, 10)}...`);
    
    log('Collecting system and software information...');
    const [systemInfo, softwareInfo] = await Promise.all([
      collectSystemInfo(),
      collectSoftwareInfo()
    ]);
    
    const payload = { system: systemInfo, software: softwareInfo };
    log(`Payload prepared with ${Object.keys(systemInfo).length} system fields and ${softwareInfo.length} software items`);
    
    log('Making HTTP POST request to API...');
    const response = await axios.post(config.apiUrl, payload, {
      headers: { 
        'Authorization': `Bearer ${config.apiToken}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Windows-Agent/1.0'
      },
      timeout: 60000, // 60 second timeout
      validateStatus: function (status) {
        return status < 500; // Accept all status codes less than 500
      }
    });
    
    const duration = Date.now() - startTime;
    log(`API request completed in ${duration}ms`);
    log(`Response status: ${response.status}`);
    log(`Response status text: ${response.statusText}`);
    
    if (response.status >= 200 && response.status < 300) {
      log(`Data sent successfully! Response: ${JSON.stringify(response.data)}`);
    } else {
      log(`API returned error status ${response.status}: ${response.statusText}`, true);
      log(`Error response body: ${JSON.stringify(response.data)}`, true);
    }
    
  } catch (e) {
    const duration = Date.now() - startTime;
    log(`=== DATA SEND FAILED after ${duration}ms ===`, true);
    
    if (e.code) {
      log(`Error code: ${e.code}`, true);
    }
    
    if (e.response) {
      // Server responded with error status
      log(`HTTP Error Response:`, true);
      log(`Status: ${e.response.status}`, true);
      log(`Status Text: ${e.response.statusText}`, true);
      log(`Response Headers: ${JSON.stringify(e.response.headers)}`, true);
      log(`Response Data: ${JSON.stringify(e.response.data)}`, true);
    } else if (e.request) {
      // Request was made but no response received
      log(`No response received from server`, true);
      log(`Request details: ${JSON.stringify(e.request)}`, true);
    } else {
      // Something else happened
      log(`Request setup error: ${e.message}`, true);
    }
    
    log(`Full error message: ${e.message}`, true);
    log(`Full error stack: ${e.stack}`, true);
    
    // Also log to the original error file location
    try {
      fs.appendFileSync(path.join(__dirname, 'agent-error.log'), new Date().toISOString() + ' ' + e.stack + '\n');
    } catch (logError) {
      log(`Failed to write to error log: ${logError.message}`, true);
    }
  }
}

function startAgent() {
  log('=== AGENT SERVICE STARTING ===');
  log(`Will send data every ${config.intervalHours} hours`);
  log(`Interval in milliseconds: ${config.intervalHours * 60 * 60 * 1000}`);
  
  // Send initial data immediately
  log('Sending initial data...');
  sendData();
  
  // Set up interval for subsequent sends
  const intervalMs = config.intervalHours * 60 * 60 * 1000;
  log(`Setting up interval timer for ${intervalMs}ms (${config.intervalHours} hours)`);
  
  const intervalId = setInterval(() => {
    log('=== INTERVAL TRIGGERED - SENDING DATA ===');
    sendData();
  }, intervalMs);
  
  log(`Interval timer set with ID: ${intervalId}`);
  log('=== AGENT SERVICE STARTED SUCCESSFULLY ===');
  
  // Handle process termination
  process.on('SIGINT', () => {
    log('Received SIGINT, shutting down...');
    clearInterval(intervalId);
    process.exit(0);
  });
  
  process.on('SIGTERM', () => {
    log('Received SIGTERM, shutting down...');
    clearInterval(intervalId);
    process.exit(0);
  });
  
  process.on('uncaughtException', (err) => {
    log(`Uncaught exception: ${err.message}`, true);
    log(`Uncaught exception stack: ${err.stack}`, true);
    process.exit(1);
  });
  
  process.on('unhandledRejection', (reason, promise) => {
    log(`Unhandled rejection at: ${promise}`, true);
    log(`Unhandled rejection reason: ${reason}`, true);
  });
}

if (require.main === module) {
  startAgent();
} 