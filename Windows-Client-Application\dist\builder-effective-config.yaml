directories:
  output: dist
  buildResources: build
appId: com.yourcompany.windowsagent
productName: Windows Agent
asar: false
files:
  - filter:
      - main.js
      - agent.js
      - service.js
      - agent-config.json
      - node.exe
      - installer.nsh
      - package.json
      - node_modules/**/*
extraResources:
  - from: node.exe
    to: node.exe
  - from: agent-config.json
    to: agent-config.json
  - from: agent.js
    to: agent.js
  - from: service.js
    to: service.js
win:
  target:
    - target: nsis
      arch:
        - x64
nsis:
  oneClick: false
  perMachine: true
  allowToChangeInstallationDirectory: true
  runAfterFinish: true
  include: installer.nsh
electronVersion: 37.2.3
